
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gravel Delivery Sylva NC | GravelDudeWnc.com | Driveway Gravel & Stone</title>
    <meta name="description"
        content="Professional gravel delivery in Sylva, Cullowhee, Waynesville & Franklin NC. Driveway gravel, drainage rock, road base & decorative stone. Fast delivery by GravelDudeWnc.com">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'brand-green': '#05F807',
                        'brand-green-dark': '#04D906',
                        'brand-green-light': '#7AFA7C'
                    },
                    animation: {
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'float': 'float 3s ease-in-out infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-10px)' },
                        }
                    }
                }
            }
        }
    </script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', sans-serif;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #05F807 0%, #04D906 100%);
        }

        .gradient-text {
            background: linear-gradient(135deg, #05F807 0%, #04D906 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .neon-glow {
            box-shadow: 0 0 20px rgba(5, 248, 7, 0.3), 0 0 40px rgba(5, 248, 7, 0.2);
        }

        .neon-text {
            text-shadow: 0 0 10px rgba(5, 248, 7, 0.5);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        .quote-glow {
            box-shadow: 0 0 30px rgba(5, 248, 7, 0.4), 0 0 60px rgba(5, 248, 7, 0.2), 0 0 90px rgba(5, 248, 7, 0.1);
        }
    </style>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": "Gravel Dude WNC",
        "image": "https://graveldudewnc.com/images/logo.png",
        "description": "Professional gravel delivery service in Western North Carolina",
        "address": {
            "@type": "PostalAddress",
            "addressLocality": "Sylva",
            "addressRegion": "NC",
            "addressCountry": "US"
        },
        "telephone": "(*************",
        "url": "https://graveldudewnc.com",
        "areaServed": ["Sylva", "Cullowhee", "Waynesville", "Franklin"],
        "serviceType": ["Gravel Delivery", "Driveway Gravel", "Drainage Rock", "Road Base", "Decorative Stone"]
    }
    </script>
</head>

<body class="font-sans text-gray-800 bg-gray-50">
    <!-- Header with Sticky Navigation -->
    <header class="sticky top-0 z-[9990] bg-gray-900/95 backdrop-blur-md shadow-xl border-b border-brand-green/20">
        <div class="flex justify-between items-center px-4 py-4 md:px-6 max-w-7xl mx-auto">
            <!-- Text Logo -->
            <div class="floating">
                <a href="#home" class="block">
                    <div class="text-2xl md:text-3xl font-black">
                        <span class="text-brand-green neon-text">Gravel</span><span class="text-white">Dude</span>
                        <div class="text-xs md:text-sm font-bold text-brand-green tracking-widest uppercase">Western NC
                        </div>
                    </div>
                </a>
            </div>

            <div class="hidden md:flex space-x-6 items-center">
                <a href="#services"
                    class="text-gray-300 hover:text-brand-green transition-all duration-300 font-medium">Services</a>
                <a href="#about"
                    class="text-gray-300 hover:text-brand-green transition-all duration-300 font-medium">Why Us</a>
                <a href="#quote"
                    class="gradient-bg text-black px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-semibold neon-glow hover:scale-105">
                    💰 FREE QUOTE
                </a>
                <a href="tel:8285551234"
                    class="bg-white text-gray-900 px-6 py-3 rounded-xl hover:bg-gray-100 transition-all duration-300 font-semibold hover:scale-105">
                    📞 Call Now
                </a>
            </div>
            <button id="menuToggle"
                class="md:hidden focus:outline-none bg-brand-green p-3 rounded-lg hover:bg-brand-green-dark transition-colors relative z-[9995]">
                <svg class="h-6 w-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M4 6h16M4 12h16M4 18h16">
                    </path>
                </svg>
            </button>
        </div>
    </header>

    <!-- Overlay -->
    <div id="overlay" class="fixed inset-0 bg-black bg-opacity-75 z-[99998] hidden transition-opacity duration-300">
    </div>

    <!-- Off-Canvas Mobile Menu -->
    <div id="mobileMenu"
        class="fixed top-0 left-0 h-full w-80 max-w-sm bg-gray-900 text-white transform -translate-x-full transition-transform duration-300 ease-in-out z-[99999] overflow-y-auto shadow-2xl">
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
            <!-- Text Logo for Mobile Menu -->
            <div>
                <div class="text-2xl font-black">
                    <span class="text-brand-green neon-text">Gravel</span><span class="text-white">Dude</span>
                    <div class="text-xs font-bold text-brand-green tracking-widest uppercase">Western NC</div>
                </div>
            </div>
            <button id="closeMenu"
                class="text-white hover:text-gray-300 focus:outline-none bg-red-500 hover:bg-red-600 rounded-full w-10 h-10 flex items-center justify-center transition-colors">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                    </path>
                </svg>
            </button>
        </div>

        <div class="p-6">
            <a href="#quote"
                class="block w-full text-center bg-brand-green text-black py-4 px-6 rounded-xl text-lg font-bold neon-glow mb-6 hover:bg-brand-green-dark transition-colors">
                💰 GET FREE QUOTE
            </a>

            <nav class="space-y-4">
                <a href="#services"
                    class="block text-lg py-3 px-4 text-gray-300 hover:text-brand-green hover:bg-gray-800 rounded-lg transition-colors">
                    🪨 Services
                </a>
                <a href="#about"
                    class="block text-lg py-3 px-4 text-gray-300 hover:text-brand-green hover:bg-gray-800 rounded-lg transition-colors">
                    🏆 Why Choose Us
                </a>
                <a href="tel:8285551234"
                    class="block text-lg py-3 px-4 text-gray-300 hover:text-brand-green hover:bg-gray-800 rounded-lg transition-colors">
                    📞 Call Now
                </a>
            </nav>

            <div class="mt-8 p-4 bg-gray-800 rounded-lg border border-brand-green/30">
                <p class="text-brand-green text-sm font-bold mb-2">🚛 Serving All of WNC</p>
                <p class="text-gray-300 text-sm mb-3">Sylva, North Carolina • Cullowhee, North Carolina • Waynesville,
                    North Carolina • Maggie Valley, North Carolina • Franklin, North Carolina • Otto, North Carolina •
                    Whittier, North Carolina • Cherokee, North Carolina • Bryson City, North Carolina</p>
                <a href="tel:8285551234" class="text-brand-green text-xl font-bold neon-text block">📞 (828)
                    555-1234</a>
                <a href="mailto:<EMAIL>"
                    class="text-gray-300 text-sm hover:text-brand-green transition-colors block mt-2">📧
                    <EMAIL></a>
            </div>
        </div>
    </div>
    <!-- Hero Section with Video Background -->
    <section id="home" class="relative h-screen flex items-center justify-center overflow-hidden">
        <video autoplay muted loop class="absolute inset-0 w-full h-full object-cover">
            <source src="videos/gravel-delivery-hero.mp4" type="video/mp4">
        </video>
        <div class="absolute inset-0 bg-gradient-to-r from-black/80 via-black/60 to-black/80"></div>
        <div class="relative z-10 text-center text-white px-4 max-w-6xl mx-auto">
            <div class="mb-6">
                <span
                    class="inline-block bg-brand-green text-black px-6 py-2 rounded-full text-sm font-bold uppercase tracking-wide animate-bounce-slow">
                    ⚡ Premium Gravel Delivery
                </span>
            </div>
            <h1 class="text-5xl md:text-7xl font-black mb-6 leading-tight">
                Professional Gravel Delivery in
                <span class="gradient-text neon-text block mt-2">Western North Carolina</span>
            </h1>
            <h2 class="text-xl md:text-3xl mb-8 font-light max-w-4xl mx-auto leading-relaxed">
                🚛 Fast Delivery • 💎 Premium Materials • 🏔️ Local WNC Experts
                <br class="hidden md:block">
                <span class="text-brand-green font-semibold">Serving Sylva, Cullowhee, Waynesville, Maggie Valley,
                    Franklin, Otto, Whittier, Cherokee & Bryson City, North Carolina</span>
            </h2>
            <div class="flex flex-col sm:flex-row gap-6 justify-center mb-8">
                <a href="#quote"
                    class="gradient-bg text-black px-10 py-5 rounded-2xl text-xl font-black hover:scale-110 transition-all duration-300 shadow-2xl neon-glow animate-pulse-slow">
                    💰 GET FREE GRAVEL QUOTE
                </a>
                <a href="tel:8285551234"
                    class="border-3 border-brand-green text-brand-green bg-black/50 px-10 py-5 rounded-2xl text-xl font-bold hover:bg-brand-green hover:text-black transition-all duration-300 shadow-2xl hover:scale-110">
                    📞 CALL (*************
                </a>
            </div>
            <div class="text-center">
                <a href="#quote"
                    class="inline-flex items-center text-brand-green font-bold hover:text-white transition-colors">
                    <span class="mr-2">👇 Scroll for Instant Quote</span>
                    <i class="fas fa-arrow-down animate-bounce"></i>
                </a>
            </div>
        </div>
    </section>

    <!-- Floating CTA Banner -->
    <div class="fixed bottom-4 left-4 right-4 z-40 md:hidden">
        <a href="#quote"
            class="block w-full gradient-bg text-black text-center py-4 rounded-2xl font-black text-lg shadow-2xl neon-glow animate-pulse">
            💰 GET YOUR FREE QUOTE NOW
        </a>
    </div>

    <!-- Intro Section -->
    <section class="py-20 bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=" 60" height="60" viewBox="0 0 60 60"
            xmlns="http://www.w3.org/2000/svg" %3E%3Cg fill="none" fill-rule="evenodd" %3E%3Cg fill="%2305F807"
            fill-opacity="0.1" %3E%3Ccircle cx="30" cy="30" r="2" /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        <div class="max-w-5xl mx-auto px-4 text-center relative z-10">
            <h2 class="text-4xl md:text-5xl font-black mb-8">
                🏆 <span class="gradient-text">Western North Carolina's</span> #1 Gravel Experts
            </h2>
            <p class="text-xl leading-relaxed text-gray-300">
                At <span class="text-brand-green font-bold neon-text">GravelDudeWnc.com</span>, we deliver premium
                gravel throughout
                <span class="text-brand-green font-semibold mb-8">Sylva, Cullowhee, Waynesville, Maggie Valley, Franklin,
                    Otto, Whittier, Cherokee, and Bryson City, North Carolina</span>.
                Whether you need driveway gravel, drainage solutions, or decorative stone, our local mountain expertise
                ensures your project gets the right materials delivered fast! 🚛💨
            </p>
            <div class="grid md:grid-cols-3 gap-6 mb-8 mt-8">
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-brand-green/30">
                    <div class="text-3xl mb-2">⚡</div>
                    <div class="text-brand-green font-bold text-2xl">Same Day</div>
                    <div class="text-gray-300">Delivery Available</div>
                </div>
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-brand-green/30">
                    <div class="text-3xl mb-2">💎</div>
                    <div class="text-brand-green font-bold text-2xl">Premium</div>
                    <div class="text-gray-300">Quality Materials</div>
                </div>
                <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-brand-green/30">
                    <div class="text-3xl mb-2">🏔️</div>
                    <div class="text-brand-green font-bold text-2xl">Local</div>
                    <div class="text-gray-300">WNC Experts</div>
                </div>
            </div>
            <a href="#quote"
                class="inline-block gradient-bg text-black px-8 py-4 rounded-2xl text-lg font-black hover:scale-110 transition-all duration-300 neon-glow">
                🎯 GET MY INSTANT QUOTE
            </a>
        </div>
    </section>

    <!-- MAIN ATTRACTION - Lead Capture Form -->
    <section id="quote" class="py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50 relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=" 100" height="100" viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg" %3E%3Cpath
            d="M50 5L55 25L75 25L59 40L64 60L50 47L36 60L41 40L25 25L45 25Z" fill="%2305F807" fill-opacity="0.03"
            /%3E%3C/svg%3E')] opacity-50"></div>
        <div class="max-w-4xl mx-auto px-4 relative z-10">
            <!-- Attention-Grabbing Header -->
            <div class="text-center mb-12">
                <div
                    class="inline-block bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-2 rounded-full text-sm font-bold uppercase tracking-wide mb-4 animate-pulse">
                    🔥 LIMITED TIME - FREE QUOTES
                </div>
                <h2 class="text-5xl md:text-6xl font-black mb-6 leading-tight">
                    Get Your <span class="gradient-text">FREE</span> Gravel Quote in
                    <span class="text-red-500">60 Seconds!</span>
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    🚛 <strong>Fast Delivery</strong> • 💰 <strong>Competitive Pricing</strong> • 📞 <strong>Local
                        Support</strong>
                    <br>Fill out the form below and we'll get back to you within 2 hours!
                </p>
            </div>

            <!-- The Star Form -->
            <div class="bg-white rounded-3xl p-8 md:p-12 shadow-2xl quote-glow border-2 border-brand-green/20 relative">
                <div class="absolute -top-6 left-1/2 transform -translate-x-1/2">
                    <div class="bg-brand-green text-black px-10 py-3 rounded-full font-black text-lg animate-bounce">
                        💎 FREE QUOTE FORM
                    </div>
                </div>

                <form action="form.php" method="POST" class="space-y-8 mt-6">
                    <!-- Honeypot Fields -->
                    <input type="text" name="website" style="display:none">
                    <input type="email" name="email_confirm" style="display:none">

                    <!-- Progress Bar -->
                    <div class="bg-gray-200 rounded-full h-3 mb-8">
                        <div class="gradient-bg h-3 rounded-full w-0 transition-all duration-500" id="progressBar">
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="relative">
                            <label for="name"
                                class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">👤 Full Name
                                *</label>
                            <input type="text" id="name" name="name" required
                                class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium"
                                placeholder="Enter your full name">
                            <div class="absolute right-3 top-12 text-brand-green opacity-0 transition-opacity duration-300"
                                id="nameCheck">✓</div>
                        </div>
                        <div class="relative">
                            <label for="phone"
                                class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📞 Phone
                                Number *</label>
                            <input type="tel" id="phone" name="phone" required
                                class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium"
                                placeholder="(*************">
                            <div class="absolute right-3 top-12 text-brand-green opacity-0 transition-opacity duration-300"
                                id="phoneCheck">✓</div>
                        </div>
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="relative">
                            <label for="email"
                                class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📧 Email
                                Address *</label>
                            <input type="email" id="email" name="email" required
                                class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium"
                                placeholder="<EMAIL>">
                            <div class="absolute right-3 top-12 text-brand-green opacity-0 transition-opacity duration-300"
                                id="emailCheck">✓</div>
                        </div>
                        <div>
                            <label for="material"
                                class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">🪨 Gravel
                                Type *</label>
                            <select id="material" name="material" required
                                class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium">
                                <option value="">Select Gravel Type</option>
                                <option value="Driveway Gravel" selected>🚗 Driveway Gravel</option>
                                <option value="Drainage Rock">💧 Drainage Rock</option>
                                <option value="Road Base">🛣️ Road Base</option>
                                <option value="Decorative Stone">💎 Decorative Stone</option>
                                <option value="Mixed Gravel">🔄 Mixed Gravel</option>
                                <option value="Not Sure">❓ Not Sure - Help Me Choose</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="address"
                            class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📍 Delivery
                            Address *</label>
                        <input type="text" id="address" name="address" required
                            class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium"
                            placeholder="123 Mountain View Dr, Sylva, NC 28779">
                    </div>

                    <div class="grid md:grid-cols-2 gap-8">
                        <div>
                            <label for="project_type"
                                class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">🔨 Project
                                Type *</label>
                            <select id="project_type" name="project_type" required
                                class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium">
                                <option value="">Select Project Type</option>
                                <option value="New Driveway">🆕 New Driveway</option>
                                <option value="Driveway Repair">🔧 Driveway Repair</option>
                                <option value="Drainage Project">💧 Drainage Project</option>
                                <option value="Landscaping">🌿 Landscaping</option>
                                <option value="Road Repair">🛣️ Road Repair</option>
                                <option value="Other">📋 Other</option>
                            </select>
                        </div>
                        <div>
                            <label for="timeline"
                                class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">⏰ When Do You
                                Need It?</label>
                            <select id="timeline" name="timeline"
                                class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg font-medium">
                                <option value="ASAP">🚨 ASAP</option>
                                <option value="This Week">📅 This Week</option>
                                <option value="Next Week">📆 Next Week</option>
                                <option value="This Month">🗓️ This Month</option>
                                <option value="Just Planning">💭 Just Planning</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <label for="details"
                            class="block text-sm font-bold text-gray-700 mb-3 uppercase tracking-wide">📝 Project
                            Details</label>
                        <textarea id="details" name="details" rows="4"
                            class="w-full px-6 py-4 border-2 border-gray-300 rounded-xl focus:ring-4 focus:ring-brand-green/30 focus:border-brand-green transition-all duration-300 text-lg"
                            placeholder="Tell us about your project: How much gravel do you need? Driveway length? Any specific requirements?"></textarea>
                    </div>

                    <!-- Captcha Container -->
                    <div id="captcha-container" class="flex justify-center"></div>

                    <div class="text-center">
                        <button type="submit"
                            class="gradient-bg text-black py-6 px-12 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 shadow-2xl neon-glow w-full md:w-auto">
                            🚀 GET MY FREE QUOTE NOW!
                        </button>
                        <p class="mt-4 text-sm text-gray-500">
                            ⚡ <strong>Fast Response:</strong> We'll contact you within 2 hours! • 🔒 <strong>100%
                                Secure</strong>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Trust Badges Below Form -->
            <div class="mt-12 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">⭐</div>
                    <div class="font-bold text-sm">5-Star Rated</div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">🚛</div>
                    <div class="font-bold text-sm">Same Day Delivery</div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">💰</div>
                    <div class="font-bold text-sm">Best Prices</div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-lg">
                    <div class="text-2xl mb-2">🏔️</div>
                    <div class="font-bold text-sm">Local WNC</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services"
        class="py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-black text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=" 80" height="80" viewBox="0 0 80 80"
            xmlns="http://www.w3.org/2000/svg" %3E%3Cg fill="none" fill-rule="evenodd" %3E%3Cg fill="%2305F807"
            fill-opacity="0.1" %3E%3Cpath d="M40 0L44 16L60 16L48 26L52 42L40 34L28 42L32 26L20 16L36 16Z"
            /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">
                    🪨 Premium <span class="gradient-text">Gravel Services</span>
                </h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    From driveways to drainage, we deliver the highest quality gravel materials throughout Western North
                    Carolina
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Driveway Gravel -->
                <div
                    class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-green/30 hover:border-brand-green transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div class="text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300">
                        🚗
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center text-brand-green">Driveway Gravel</h3>
                    <p class="text-gray-300 text-center mb-6 leading-relaxed">Perfect for new driveways and repairs.
                        Compacts well and provides excellent drainage in our WNC mountain climate.</p>
                    <div class="text-center">
                        <a href="#quote"
                            class="inline-block bg-brand-green text-black px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>

                <!-- Drainage Rock -->
                <div
                    class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-green/30 hover:border-brand-green transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div class="text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300">
                        💧
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center text-brand-green">Drainage Rock</h3>
                    <p class="text-gray-300 text-center mb-6 leading-relaxed">Specially sized for optimal water flow.
                        Essential for preventing erosion and water damage in mountain terrain.</p>
                    <div class="text-center">
                        <a href="#quote"
                            class="inline-block bg-brand-green text-black px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>

                <!-- Road Base -->
                <div
                    class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-green/30 hover:border-brand-green transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div class="text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300">
                        🛣️
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center text-brand-green">Road Base</h3>
                    <p class="text-gray-300 text-center mb-6 leading-relaxed">Heavy-duty foundation material for roads,
                        parking areas, and high-traffic surfaces that need to last.</p>
                    <div class="text-center">
                        <a href="#quote"
                            class="inline-block bg-brand-green text-black px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>

                <!-- Decorative Stone -->
                <div
                    class="group bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-brand-green/30 hover:border-brand-green transition-all duration-300 hover:scale-105 hover:shadow-2xl">
                    <div class="text-6xl mb-6 text-center group-hover:scale-110 transition-transform duration-300">
                        💎
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-center text-brand-green">Decorative Stone</h3>
                    <p class="text-gray-300 text-center mb-6 leading-relaxed">Beautiful natural stone to enhance
                        landscaping, walkways, and outdoor living spaces.</p>
                    <div class="text-center">
                        <a href="#quote"
                            class="inline-block bg-brand-green text-black px-6 py-3 rounded-xl font-bold hover:scale-110 transition-all duration-300">
                            Get Quote
                        </a>
                    </div>
                </div>
            </div>

            <!-- CTA Below Services -->
            <div class="text-center mt-16">
                <a href="#quote"
                    class="inline-block gradient-bg text-black px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 neon-glow">
                    🎯 GET PRICING FOR ALL SERVICES
                </a>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section id="about" class="py-20 bg-white relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=" 60" height="60" viewBox="0 0 60 60"
            xmlns="http://www.w3.org/2000/svg" %3E%3Cg fill="none" fill-rule="evenodd" %3E%3Cg fill="%2305F807"
            fill-opacity="0.05" %3E%3Ccircle cx="30" cy="30" r="3" /%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-50"></div>
        <div class="max-w-7xl mx-auto px-4 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">
                    🏆 Why <span class="gradient-text">Gravel Dude WNC</span> Leads the Pack
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    We're not just another gravel company - we're your local Western North Carolina mountain
                    specialists!
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center group">
                    <div
                        class="bg-gradient-to-br from-brand-green to-brand-green-dark text-black rounded-3xl p-8 mb-6 group-hover:scale-110 transition-all duration-300 shadow-xl">
                        <div class="text-6xl">💎</div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Premium Quality Materials</h3>
                    <p class="text-gray-600 leading-relaxed">We source only the highest grade gravel and stone from
                        trusted local quarries, ensuring your project lasts for decades.</p>
                </div>

                <div class="text-center group">
                    <div
                        class="bg-gradient-to-br from-brand-green to-brand-green-dark text-black rounded-3xl p-8 mb-6 group-hover:scale-110 transition-all duration-300 shadow-xl">
                        <div class="text-6xl">⚡</div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Lightning Fast Delivery</h3>
                    <p class="text-gray-600 leading-relaxed">Same-day and next-day delivery available throughout WNC.
                        When you need gravel, we get it there FAST!</p>
                </div>

                <div class="text-center group">
                    <div
                        class="bg-gradient-to-br from-brand-green to-brand-green-dark text-black rounded-3xl p-8 mb-6 group-hover:scale-110 transition-all duration-300 shadow-xl">
                        <div class="text-6xl">🏔️</div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Local Mountain Expertise</h3>
                    <p class="text-gray-600 leading-relaxed">Born and raised in WNC! We understand mountain weather,
                        terrain, and exactly what materials work best here.</p>
                </div>

                <div class="text-center group">
                    <div
                        class="bg-gradient-to-br from-brand-green to-brand-green-dark text-black rounded-3xl p-8 mb-6 group-hover:scale-110 transition-all duration-300 shadow-xl">
                        <div class="text-6xl">💰</div>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Unbeatable Pricing</h3>
                    <p class="text-gray-600 leading-relaxed">Fair, transparent pricing with no hidden fees. We guarantee
                        competitive rates that won't break your budget.</p>
                </div>
            </div>

            <!-- Stats Row -->
            <div class="mt-16 bg-gray-50 rounded-3xl p-8">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                    <div>
                        <div class="text-4xl font-black text-brand-green mb-2">500+</div>
                        <div class="text-gray-600 font-semibold">Happy Customers</div>
                    </div>
                    <div>
                        <div class="text-4xl font-black text-brand-green mb-2">50+</div>
                        <div class="text-gray-600 font-semibold">Towns Served</div>
                    </div>
                    <div>
                        <div class="text-4xl font-black text-brand-green mb-2">24hr</div>
                        <div class="text-gray-600 font-semibold">Response Time</div>
                    </div>
                    <div>
                        <div class="text-4xl font-black text-brand-green mb-2">⭐⭐⭐⭐⭐</div>
                        <div class="text-gray-600 font-semibold">5-Star Rating</div>
                    </div>
                </div>
            </div>

            <!-- Final CTA -->
            <div class="text-center mt-16">
                <a href="#quote"
                    class="inline-block gradient-bg text-black px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 neon-glow">
                    🚀 EXPERIENCE THE DIFFERENCE
                </a>
            </div>
        </div>
    </section>

    <!-- Local Photo Section -->
    <section class="py-20 bg-gradient-to-br from-gray-100 to-gray-200">
        <div class="max-w-7xl mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-5xl md:text-6xl font-black mb-6">
                    📸 Our Work Throughout <span class="gradient-text">Western NC</span>
                </h2>
                <p class="text-xl text-gray-600">
                    See the quality and professionalism that sets us apart across the mountains
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div
                    class="group bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <div class="relative overflow-hidden">
                        <img src="images/gravel-delivery-truck.jpg" alt="Gravel delivery truck in Sylva NC"
                            class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                            loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-brand-green font-bold">📍 Sylva, NC</div>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-center font-medium">Fresh premium gravel delivered by our
                            professional team in Waynesville, NC</p>
                        <div class="mt-4 text-center">
                            <a href="#quote" class="text-brand-green font-bold hover:underline">Get Quote for Your Area
                                →</a>
                        </div>
                    </div>
                </div>

                <div
                    class="group bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <div class="relative overflow-hidden">
                        <img src="images/driveway-gravel-installed.jpg" alt="New driveway gravel installation"
                            class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                            loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-brand-green font-bold">📍 Cullowhee, NC</div>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-center font-medium">Beautiful new driveway installation completed
                            in Cullowhee with premium gravel</p>
                        <div class="mt-4 text-center">
                            <a href="#quote" class="text-brand-green font-bold hover:underline">Get Quote for Your
                                Driveway →</a>
                        </div>
                    </div>
                </div>

                <div
                    class="group bg-white rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                    <div class="relative overflow-hidden">
                        <img src="images/drainage-rock-project.jpg" alt="Drainage rock installation Western NC"
                            class="w-full h-64 object-cover group-hover:scale-110 transition-transform duration-300"
                            loading="lazy">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <div class="text-brand-green font-bold">📍 Franklin, NC</div>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 text-center font-medium">Professional drainage rock installation solving
                            water issues in Franklin, NC</p>
                        <div class="mt-4 text-center">
                            <a href="#quote" class="text-brand-green font-bold hover:underline">Fix Your Drainage Issues
                                →</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final Mega CTA -->
    <section class="py-24 gradient-bg text-black relative overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width=" 100" height="100" viewBox="0 0 100 100"
            xmlns="http://www.w3.org/2000/svg" %3E%3Cpath
            d="M50 10L55 35L80 35L61 52L66 77L50 64L34 77L39 52L20 35L45 35Z" fill="%23000000" fill-opacity="0.1"
            /%3E%3C/svg%3E')] opacity-20"></div>
        <div class="max-w-5xl mx-auto px-4 text-center relative z-10">
            <div class="mb-8">
                <div
                    class="inline-block bg-black text-brand-green px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide animate-pulse">
                    🚨 DON'T WAIT - GET YOUR QUOTE TODAY!
                </div>
            </div>
            <h2 class="text-5xl md:text-7xl font-black mb-8 leading-tight">
                Ready to Start Your Gravel Project?
            </h2>
            <p class="text-2xl md:text-3xl mb-12 font-medium max-w-4xl mx-auto leading-relaxed">
                Join 500+ satisfied customers who chose Western North Carolina's #1 gravel delivery service!
                <br><span class="font-bold">Get your FREE quote in 60 seconds!</span>
            </p>
            <div class="flex flex-col sm:flex-row gap-8 justify-center items-center mb-8">
                <a href="#quote"
                    class="bg-black text-brand-green px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 shadow-2xl border-2 border-black hover:bg-gray-900">
                    💰 GET FREE QUOTE NOW
                </a>
                <div class="text-xl font-bold">OR</div>
                <a href="tel:8285551234"
                    class="bg-white text-black px-12 py-6 rounded-2xl text-2xl font-black hover:scale-110 transition-all duration-300 shadow-2xl">
                    📞 CALL (*************
                </a>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-3xl mx-auto text-sm font-bold">
                <div>✅ Same Day Delivery</div>
                <div>✅ Premium Materials</div>
                <div>✅ Local WNC Experts</div>
                <div>✅ Best Prices Guaranteed</div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <!-- Text Logo for Footer -->
                    <div class="mb-6">
                        <div class="text-3xl font-black">
                            <span class="text-brand-green neon-text">Gravel</span><span class="text-white">Dude</span>
                            <div class="text-sm font-bold text-brand-green tracking-widest uppercase">Western NC</div>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed mb-4">Western North Carolina's premier gravel delivery
                        service. Professional, fast, and reliable since day one.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-brand-green hover:text-white transition-colors"><i
                                class="fab fa-facebook-f text-xl"></i></a>
                        <a href="#" class="text-brand-green hover:text-white transition-colors"><i
                                class="fab fa-instagram text-xl"></i></a>
                        <a href="#" class="text-brand-green hover:text-white transition-colors"><i
                                class="fab fa-google text-xl"></i></a>
                    </div>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6 text-brand-green">🪨 Services</h4>
                    <ul class="space-y-3 text-gray-300">
                        <li><a href="#quote" class="hover:text-brand-green transition-colors">Driveway Gravel</a></li>
                        <li><a href="#quote" class="hover:text-brand-green transition-colors">Drainage Rock</a></li>
                        <li><a href="#quote" class="hover:text-brand-green transition-colors">Road Base</a></li>
                        <li><a href="#quote" class="hover:text-brand-green transition-colors">Decorative Stone</a></li>
                        <li><a href="#quote" class="hover:text-brand-green transition-colors">Emergency Delivery</a>
                        </li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6 text-brand-green">📍 Service Areas</h4>
                    <ul class="space-y-2 text-gray-300 text-sm">
                        <li>Sylva, North Carolina</li>
                        <li>Cullowhee, North Carolina</li>
                        <li>Waynesville, North Carolina</li>
                        <li>Maggie Valley, North Carolina</li>
                        <li>Franklin, North Carolina</li>
                        <li>Otto, North Carolina</li>
                        <li>Whittier, North Carolina</li>
                        <li>Cherokee, North Carolina</li>
                        <li>Bryson City, North Carolina</li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-xl font-bold mb-6 text-brand-green">📞 Contact</h4>
                    <div class="space-y-4 text-gray-300">
                        <a href="tel:8285551234" class="block hover:text-brand-green transition-colors">
                            <strong>📞 (*************</strong>
                        </a>
                        <a href="mailto:<EMAIL>" class="block hover:text-brand-green transition-colors">
                            📧 <EMAIL>
                        </a>
                        <p>🏔️ Serving All of Western NC</p>
                        <a href="#quote"
                            class="inline-block bg-brand-green text-black px-6 py-3 rounded-xl font-bold hover:scale-105 transition-all duration-300 mt-4">
                            Get Quote
                        </a>
                    </div>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
                <p>&copy; 2024 GravelDudeWnc.com - Premium Gravel Delivery Throughout Western North Carolina | All
                    Rights Reserved</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile Menu
        const menuToggle = document.getElementById('menuToggle');
        const mobileMenu = document.getElementById('mobileMenu');
        const closeMenu = document.getElementById('closeMenu');
        const overlay = document.getElementById('overlay');

        function openMenu() {
            mobileMenu.classList.remove('-translate-x-full');
            overlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeMenuHandler() {
            mobileMenu.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
            document.body.style.overflow = 'auto';
        }

        if (menuToggle) menuToggle.addEventListener('click', openMenu);
        if (closeMenu) closeMenu.addEventListener('click', closeMenuHandler);
        if (overlay) overlay.addEventListener('click', closeMenuHandler);

        // Close menu when clicking on navigation links
        document.querySelectorAll('#mobileMenu a[href^="#"]').forEach(link => {
            link.addEventListener('click', closeMenuHandler);
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    closeMenuHandler();
                }
            });
        });

        // Form Progress and Validation
        const form = document.querySelector('form');
        const progressBar = document.getElementById('progressBar');
        const requiredFields = form.querySelectorAll('input[required], select[required]');

        function updateProgress() {
            let filled = 0;
            requiredFields.forEach(field => {
                if (field.value.trim() !== '') filled++;
            });
            const progress = (filled / requiredFields.length) * 100;
            progressBar.style.width = progress + '%';
        }

        // Add validation checkmarks
        const nameField = document.getElementById('name');
        const emailField = document.getElementById('email');
        const phoneField = document.getElementById('phone');

        nameField.addEventListener('input', function () {
            const check = document.getElementById('nameCheck');
            if (this.value.length >= 2) {
                check.style.opacity = '1';
            } else {
                check.style.opacity = '0';
            }
            updateProgress();
        });

        emailField.addEventListener('input', function () {
            const check = document.getElementById('emailCheck');
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailRegex.test(this.value)) {
                check.style.opacity = '1';
            } else {
                check.style.opacity = '0';
            }
            updateProgress();
        });

        phoneField.addEventListener('input', function () {
            const check = document.getElementById('phoneCheck');
            if (this.value.length >= 10) {
                check.style.opacity = '1';
            } else {
                check.style.opacity = '0';
            }
            updateProgress();
        });

        // Update progress on all field changes
        requiredFields.forEach(field => {
            field.addEventListener('input', updateProgress);
            field.addEventListener('change', updateProgress);
        });

        // Initial progress update
        updateProgress();

        // Scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.querySelectorAll('.floating, .group').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(el);
        });
    </script>
</body>

</html>